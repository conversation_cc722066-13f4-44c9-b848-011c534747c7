<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { Skeleton } from '@/components/ui/skeleton'
import { Icon } from '#components'
import type { Contact } from '@/types/chat'
import { formatDateStr } from '~/utils/typeof'
import type { CustomerAgentClientWsService } from '~/utils/api/ws/agentClient.service'
import type { PageData, WebSocketResponse } from '~/utils/http/types'
import type { ContactGroup } from '~/types/api/params/QueryContactListParams'
import type { Platform, PlatformAccount } from '~/types/api/response/PlatformResponse'

// 定义组件的props
const props = defineProps<{
  // 当前选中的联系人
  activeContact: Contact | null
  agentClientApi: CustomerAgentClientWsService | null
  activePlatform: Platform
  selectedSubAccount: PlatformAccount | null
}>()

// 定义组件的emits
const emit = defineEmits<{
  // 选中联系人时触发
  (e: 'select-contact', contact: Contact): void
}>()

const contacts = ref<Contact[]>([])

const paginationByGroup = ref<Record<ContactGroup, { page: number, hasMore: boolean, loading: boolean }>>({
  'all': { page: 1, hasMore: true, loading: false },
  'no-order': { page: 1, hasMore: true, loading: false },
  'unpaid': { page: 1, hasMore: true, loading: false }
})

// 根据 `group` 属性对联系人进行分组
const contactGroups = computed(() => {
  return {
    'no-order': contacts.value.filter(c => c.group === 'no-order'),
    unpaid: contacts.value.filter(c => c.group === 'unpaid'),
    all: contacts.value.filter(c => c.group === 'all')
  }
})

function fetchContacts(group: ContactGroup, isReset = false) {
  const pagination = paginationByGroup.value[group]
  if (pagination.loading || !props.agentClientApi || !props.selectedSubAccount) {
    return
  }

  if (isReset) {
    // Remove contacts of this group
    contacts.value = contacts.value.filter(c => c.group !== group)
    pagination.page = 1
    pagination.hasMore = true
  }

  pagination.loading = true

  const params = {
    platformId: props.activePlatform.id,
    accountId: props.selectedSubAccount.accountId,
    group: group,
    pageNum: pagination.page,
    pageSize: 20
  }
  props.agentClientApi.queryContactList(params)
}

function handleContactListResponse(response: WebSocketResponse<PageData<Contact>>) {
  const requestId = response.requestId as ContactGroup
  const pagination = paginationByGroup.value[requestId]
  pagination.loading = false
  if (response.code !== 200) {
    message.error(response.message)
    return
  }

  if (!response.data) {
    return
  }

  // 校验响应是否与当前上下文匹配
  if (!props.selectedSubAccount || !response.data.result || response.data.result.length === 0) {
    return
  }

  const firstContact = response.data.result[0]
  if (!firstContact || firstContact.platformId !== props.activePlatform.id || firstContact.ownerAccountId !== props.selectedSubAccount.accountId) {
    // 如果响应的platformId或ownerAccountId与当前不符，则忽略此响应
    logger.warn('查询的联系人列表并不是当前激活的平台或所选择的子账号')
    return
  }

  const { result, pageNum, pages } = response.data

  const existingIds = new Set(contacts.value.map(c => c.contactId))
  const newContacts = result.filter(c => !existingIds.has(c.contactId))
  contacts.value.push(...newContacts)

  pagination.hasMore = pageNum < pages
  pagination.loading = false
}

function handleLoadMore(group: ContactGroup) {
  const pagination = paginationByGroup.value[group]
  if (!pagination.loading && pagination.hasMore) {
    pagination.page++
    fetchContacts(group)
  }
}

watch(() => props.agentClientApi, (newApi, oldApi) => {
  if (newApi && newApi !== oldApi) {
    newApi.on<PageData<Contact>>('CUSTOMER_QUERY_CONTACT_LIST', handleContactListResponse, 'all')
    newApi.on<PageData<Contact>>('CUSTOMER_QUERY_CONTACT_LIST', handleContactListResponse, 'no-order')
    newApi.on<PageData<Contact>>('CUSTOMER_QUERY_CONTACT_LIST', handleContactListResponse, 'unpaid')
    // Fetch initial data for all groups
    fetchContacts('all', true)
    fetchContacts('no-order', true)
    fetchContacts('unpaid', true)
  }
}, { immediate: true })

watch(() => props.selectedSubAccount, (newAccount, oldAccount) => {
  // 确保 newAccount 存在，并且与旧账号不同
  if (newAccount && oldAccount && newAccount.accountId !== oldAccount.accountId) {
    // 子账号变更，重置并重新获取所有联系人分组
    fetchContacts('all', true)
    fetchContacts('no-order', true)
    fetchContacts('unpaid', true)
  }
}, { deep: true })

// "全部买家" 标签页下的手风琴菜单配置
const accordionItems = [
  { value: 'no-order', title: '咨询未下单', group: 'no-order' as ContactGroup },
  { value: 'unpaid', title: '咨询未付款', group: 'unpaid' as ContactGroup },
  { value: 'all-buyers', title: '全部买家', group: 'all' as ContactGroup }
]
</script>

<template>
  <div class="flex h-full flex-col bg-slate-50 dark:bg-slate-900 border-r">
    <div class="flex-1 overflow-y-auto">
      <Accordion type="multiple" class="w-full" :default-value="['all-buyers']">
        <AccordionItem v-for="item in accordionItems" :key="item.value" :value="item.value">
          <AccordionTrigger
            class="px-3 py-2 text-sm hover:no-underline hover:bg-slate-100 dark:hover:bg-slate-800 rounded-md">
            <div class="flex items-center justify-between w-full">
              <span class="font-semibold">{{ item.title }} ({{ contactGroups[item.group]?.length || 0 }})</span>
              <div class="flex items-center gap-2">
                <Icon v-if="item.value !== 'starred'" name="lucide:refresh-cw"
                  class="h-4 w-4 text-muted-foreground hover:text-foreground"
                  @click.stop="fetchContacts(item.group, true)" />
                <Icon v-if="item.value === 'starred'" name="lucide:help-circle"
                  class="h-4 w-4 text-muted-foreground hover:text-foreground" />
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <!-- 联系人列表加载骨架屏 -->
            <div v-if="paginationByGroup[item.group].loading && contactGroups[item.group].length === 0">
              <div v-for="i in 5" :key="`contact-skeleton-${item.group}-${i}`" class="flex items-center gap-3 p-3">
                <Skeleton class="h-10 w-10 rounded-full" />
                <div class="grid gap-0.5 flex-1">
                  <div class="flex items-center justify-between">
                    <Skeleton class="h-4 w-24" />
                    <Skeleton class="h-3 w-12" />
                  </div>
                  <div class="flex items-center justify-between">
                    <Skeleton class="h-3 w-32" />
                  </div>
                </div>
              </div>
            </div>
            <!-- 实际联系人列表 -->
            <div v-else>
              <div v-for="contact in contactGroups[item.group]" :key="contact.contactId"
                class="flex items-center gap-3 p-3 cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800"
                :class="{ 'bg-slate-200 dark:bg-slate-700': props.activeContact?.contactId === contact.contactId }"
                @click="emit('select-contact', contact)">
                <Avatar class="h-10 w-10">
                  <AvatarImage :src="String(contact.contactDetail.avatar)"
                    :alt="String(contact.contactDetail.nickname)" />
                  <AvatarFallback>{{
                    contact.contactDetail.displayName ? contact.contactDetail.displayName.substring(0, 2) :
                      contact.contactDetail.userId.substring(0, 2)
                  }}
                  </AvatarFallback>
                </Avatar>
                <div class="grid gap-0.5 flex-1">
                  <div class="flex items-center justify-between">
                    <span class="font-semibold text-sm">{{
                      contact.contactDetail.displayName ? contact.contactDetail.displayName :
                        contact.contactDetail.nickname
                    }}</span>
                    <span class="text-xs text-muted-foreground">{{ formatDateStr(contact.lastSendTime) }}</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="line-clamp-1 text-xs text-muted-foreground">
                      <p v-if="contact.lastMessageMeta" class="text-gray-400">
                        {{ contact.lastMessageMeta }}
                      </p>
                      <p>{{ contact.lastMessage }}</p>
                    </div>
                    <Icon v-if="contact.starred" name="lucide:star" class="h-4 w-4 text-yellow-500 fill-yellow-500" />
                  </div>
                </div>
              </div>
              <div v-if="paginationByGroup[item.group].hasMore"
                class="flex items-center justify-center p-2 text-sm text-muted-foreground cursor-pointer hover:text-foreground"
                @click="handleLoadMore(item.group)">
                <div v-if="paginationByGroup[item.group].loading" class="flex items-center">
                  <Icon name="lucide:loader-2" class="mr-2 h-4 w-4 animate-spin" />
                  正在加载...
                </div>
                <div v-else class="flex items-center">
                  <Icon name="lucide:plus" class="mr-2 h-4 w-4" />
                  加载更多
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  </div>
</template>
